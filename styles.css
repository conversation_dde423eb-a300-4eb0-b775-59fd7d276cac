/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Section styles */
.celebrations-section {
    padding: 80px 0;
    background-color: #ffffff;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 400;
    text-align: center;
    margin-bottom: 60px;
    color: #333;
    letter-spacing: -0.02em;
}

/* Grid layout */
.celebrations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    max-width: 1000px;
    margin: 0 auto;
}

/* Card styles */
.celebration-card {
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.celebration-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
}

.celebration-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.02) 0%, rgba(255, 215, 0, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.celebration-card:hover::before {
    opacity: 1;
}

.card-content {
    position: relative;
    z-index: 1;
}

/* Icon styles */
.icon-container {
    margin-bottom: 25px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.celebration-icon {
    width: 80px;
    height: 80px;
    transition: transform 0.3s ease;
}

.celebration-card:hover .celebration-icon {
    transform: scale(1.1);
}

/* Card title */
.card-title {
    font-size: 1.5rem;
    font-weight: 500;
    color: #333;
    margin: 0;
    letter-spacing: -0.01em;
}

/* Responsive design */
@media (max-width: 768px) {
    .celebrations-section {
        padding: 60px 0;
    }
    
    .section-title {
        font-size: 2rem;
        margin-bottom: 40px;
    }
    
    .celebrations-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .celebration-card {
        padding: 30px 20px;
    }
    
    .celebration-icon {
        width: 60px;
        height: 60px;
    }
    
    .card-title {
        font-size: 1.25rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .celebration-card {
        padding: 25px 15px;
    }
}

/* Animation for page load */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.celebration-card {
    animation: fadeInUp 0.6s ease forwards;
}

.celebration-card:nth-child(1) {
    animation-delay: 0.1s;
}

.celebration-card:nth-child(2) {
    animation-delay: 0.2s;
}

.celebration-card:nth-child(3) {
    animation-delay: 0.3s;
}

/* Active state */
.celebration-card.active {
    border-color: #ff6b35;
    box-shadow: 0 5px 20px rgba(255, 107, 53, 0.2);
}

.celebration-card.active .card-title {
    color: #ff6b35;
}
